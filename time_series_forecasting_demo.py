"""
Fast Demo: Time Series Forecasting - ARIMA/SARIMAX vs Holt-Winter
Electric Demand Forecasting with Exogenous Variables
Optimized for Quick Execution
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

# Statistical and Time Series Libraries
from statsmodels.tsa.stattools import adfuller
from statsmodels.tsa.seasonal import seasonal_decompose
from statsmodels.tsa.statespace.sarimax import SARIMAX
from statsmodels.tsa.holtwinters import ExponentialSmoothing
from statsmodels.graphics.tsaplots import plot_acf, plot_pacf
from statsmodels.stats.diagnostic import acorr_ljungbox

# Metrics
from sklearn.metrics import mean_squared_error, mean_absolute_error

# Set style for better plots
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

class FastTimeSeriesForecaster:
    def __init__(self, data_path, sample_size=5000):
        """Initialize the forecaster with data path and sample size for faster execution"""
        self.data_path = data_path
        self.sample_size = sample_size
        self.df = None
        self.train_data = None
        self.test_data = None
        self.validation_data = None
        self.sarimax_model = None
        self.holt_winter_model = None
        
    def calculate_metrics(self, actual, forecast):
        """Calculate evaluation metrics"""
        rmse = np.sqrt(mean_squared_error(actual, forecast))
        mae = mean_absolute_error(actual, forecast)
        mape = np.mean(np.abs((actual - forecast) / actual)) * 100
        return {'RMSE': rmse, 'MAE': mae, 'MAPE': mape}
    
    def load_and_prepare_data(self):
        """Load and prepare the dataset with proper datetime indexing"""
        print("=" * 60)
        print("PHASE 1: DATA PREPARATION AND EXPLORATORY DATA ANALYSIS")
        print("=" * 60)
        
        # Load data
        print("1. Loading electric demand data...")
        df_full = pd.read_csv(self.data_path)
        
        # Take a sample for faster execution
        print(f"2. Sampling {self.sample_size} records for demo...")
        self.df = df_full.tail(self.sample_size).copy()
        
        # Create datetime index
        print("3. Creating proper datetime index...")
        self.df['DateTime'] = pd.to_datetime(self.df[['Year', 'Month', 'Day', 'Hour']])
        self.df.set_index('DateTime', inplace=True)
        self.df.index.freq = 'H'
        
        print(f"   Data shape: {self.df.shape}")
        print(f"   Date range: {self.df.index[0]} to {self.df.index[-1]}")
        
        return self.df
    
    def handle_exogenous_variables(self):
        """Encode exogenous variables properly"""
        print("\n4. Handling exogenous variables...")
        
        # One-hot encode WeekDay (drop WeekDay_1 to avoid multicollinearity)
        weekday_dummies = pd.get_dummies(self.df['WeekDay'], prefix='WeekDay', drop_first=True)
        self.df = pd.concat([self.df, weekday_dummies], axis=1)
        
        # Use only Temperature as exogenous variable for faster execution
        self.exog_vars = ['Temperature']
        
        # Ensure all exogenous variables are numeric
        for col in self.exog_vars:
            if col in self.df.columns:
                self.df[col] = pd.to_numeric(self.df[col], errors='coerce').astype('float64')
        
        print(f"   Exogenous variables (simplified): {self.exog_vars}")
        
        return self.df
    
    def exploratory_data_analysis(self):
        """Quick EDA with key visualizations"""
        print("\n5. Performing Quick Exploratory Data Analysis...")
        
        # Create figure with subplots
        fig, axes = plt.subplots(2, 2, figsize=(12, 8))
        fig.suptitle('Electric Demand - Quick EDA', fontsize=14, fontweight='bold')
        
        # 1. Demand over time
        axes[0,0].plot(self.df.index, self.df['Demand'], alpha=0.8, linewidth=1)
        axes[0,0].set_title('Electric Demand Over Time')
        axes[0,0].set_ylabel('Demand')
        axes[0,0].tick_params(axis='x', rotation=45)
        
        # 2. Demand vs Temperature
        axes[0,1].scatter(self.df['Temperature'], self.df['Demand'], alpha=0.6, s=2)
        axes[0,1].set_title('Demand vs Temperature')
        axes[0,1].set_xlabel('Temperature')
        axes[0,1].set_ylabel('Demand')
        
        # 3. Average demand by hour
        hourly_demand = self.df.groupby('Hour')['Demand'].mean()
        axes[1,0].plot(hourly_demand.index, hourly_demand.values, marker='o')
        axes[1,0].set_title('Average Demand by Hour')
        axes[1,0].set_xlabel('Hour')
        axes[1,0].set_ylabel('Average Demand')
        axes[1,0].grid(True, alpha=0.3)
        
        # 4. Average demand by weekday
        weekday_demand = self.df.groupby('WeekDay')['Demand'].mean()
        axes[1,1].bar(weekday_demand.index, weekday_demand.values)
        axes[1,1].set_title('Average Demand by Day of Week')
        axes[1,1].set_xlabel('WeekDay (1=Mon, 7=Sun)')
        axes[1,1].set_ylabel('Average Demand')
        
        plt.tight_layout()
        plt.show()
    
    def split_data_chronologically(self):
        """Split data chronologically: 70% train, 20% test, 10% validation"""
        print("\n" + "=" * 60)
        print("PHASE 2: DATA SPLITTING AND MODEL TRAINING")
        print("=" * 60)
        
        print("6. Splitting data chronologically (70/20/10)...")
        
        n = len(self.df)
        train_size = int(0.7 * n)
        test_size = int(0.2 * n)
        
        self.train_data = self.df.iloc[:train_size].copy()
        self.test_data = self.df.iloc[train_size:train_size + test_size].copy()
        self.validation_data = self.df.iloc[train_size + test_size:].copy()
        
        print(f"   Training set: {len(self.train_data)} samples")
        print(f"   Testing set: {len(self.test_data)} samples")
        print(f"   Validation set: {len(self.validation_data)} samples")
    
    def train_sarimax_model(self):
        """Train SARIMAX model with simple parameters"""
        print("\n7. Training SARIMAX model...")
        
        # Test stationarity
        result = adfuller(self.train_data['Demand'].dropna())
        print(f"   ADF p-value: {result[1]:.6f}")
        
        # Use simple parameters for faster execution
        order = (1, 1, 1)
        seasonal_order = (1, 1, 1, 24)
        
        print(f"   Using SARIMAX order: {order}")
        print(f"   Using seasonal order: {seasonal_order}")
        
        # Prepare exogenous variables
        exog_train = self.train_data[self.exog_vars].copy()
        
        # Train SARIMAX model
        try:
            self.sarimax_model = SARIMAX(
                self.train_data['Demand'].astype('float64'),
                exog=exog_train,
                order=order,
                seasonal_order=seasonal_order
            ).fit(disp=False)
            print("   SARIMAX model trained successfully!")
        except Exception as e:
            print(f"   Error: {e}")
            print("   Training without exogenous variables...")
            self.sarimax_model = SARIMAX(
                self.train_data['Demand'].astype('float64'),
                order=order,
                seasonal_order=seasonal_order
            ).fit(disp=False)
            self.exog_vars = []
            print("   SARIMAX model (no exog) trained successfully!")
        
        return self.sarimax_model
    
    def train_holt_winter_model(self):
        """Train Holt-Winter model"""
        print("\n8. Training Holt-Winter model...")
        
        self.holt_winter_model = ExponentialSmoothing(
            self.train_data['Demand'],
            trend='add',
            seasonal='add',
            seasonal_periods=24
        ).fit()
        
        print("   Holt-Winter model trained successfully!")
        return self.holt_winter_model
    
    def evaluate_and_compare_models(self):
        """Evaluate and compare both models"""
        print("\n" + "=" * 60)
        print("PHASE 3 & 4: EVALUATION AND COMPARISON")
        print("=" * 60)
        
        print("9. Evaluating models on test set...")
        
        # Prepare test data
        if self.exog_vars:
            exog_test = self.test_data[self.exog_vars].copy()
            sarimax_pred_test = self.sarimax_model.predict(
                start=self.test_data.index[0],
                end=self.test_data.index[-1],
                exog=exog_test
            )
        else:
            sarimax_pred_test = self.sarimax_model.predict(
                start=self.test_data.index[0],
                end=self.test_data.index[-1]
            )
        
        holt_pred_test = self.holt_winter_model.forecast(steps=len(self.test_data))
        holt_pred_test.index = self.test_data.index
        
        # Calculate metrics
        sarimax_metrics = self.calculate_metrics(self.test_data['Demand'], sarimax_pred_test)
        holt_metrics = self.calculate_metrics(self.test_data['Demand'], holt_pred_test)
        
        print("   Test Set Performance:")
        print(f"   SARIMAX - RMSE: {sarimax_metrics['RMSE']:.2f}, MAE: {sarimax_metrics['MAE']:.2f}, MAPE: {sarimax_metrics['MAPE']:.2f}%")
        print(f"   Holt-Winter - RMSE: {holt_metrics['RMSE']:.2f}, MAE: {holt_metrics['MAE']:.2f}, MAPE: {holt_metrics['MAPE']:.2f}%")
        
        # Validation forecasts
        print("\n10. Final validation forecasts...")
        
        # Retrain on train+test
        full_train = pd.concat([self.train_data, self.test_data])
        
        if self.exog_vars:
            exog_full = full_train[self.exog_vars].copy()
            sarimax_final = SARIMAX(
                full_train['Demand'].astype('float64'),
                exog=exog_full,
                order=self.sarimax_model.specification['order'],
                seasonal_order=self.sarimax_model.specification['seasonal_order']
            ).fit(disp=False)
        else:
            sarimax_final = SARIMAX(
                full_train['Demand'].astype('float64'),
                order=self.sarimax_model.specification['order'],
                seasonal_order=self.sarimax_model.specification['seasonal_order']
            ).fit(disp=False)
        
        holt_final = ExponentialSmoothing(
            full_train['Demand'],
            trend='add',
            seasonal='add',
            seasonal_periods=24
        ).fit()
        
        # 24-hour forecasts
        steps_24h = min(24, len(self.validation_data))
        if self.exog_vars:
            exog_val = self.validation_data[self.exog_vars].iloc[:steps_24h].copy()
            sarimax_24h = sarimax_final.forecast(steps=steps_24h, exog=exog_val)
        else:
            sarimax_24h = sarimax_final.forecast(steps=steps_24h)
        
        holt_24h = holt_final.forecast(steps=steps_24h)
        actual_24h = self.validation_data['Demand'].iloc[:steps_24h]
        
        sarimax_24h.index = actual_24h.index
        holt_24h.index = actual_24h.index
        
        sarimax_metrics_24h = self.calculate_metrics(actual_24h, sarimax_24h)
        holt_metrics_24h = self.calculate_metrics(actual_24h, holt_24h)
        
        print(f"\n   24-Hour Forecast Performance:")
        print(f"   SARIMAX - RMSE: {sarimax_metrics_24h['RMSE']:.2f}, MAE: {sarimax_metrics_24h['MAE']:.2f}, MAPE: {sarimax_metrics_24h['MAPE']:.2f}%")
        print(f"   Holt-Winter - RMSE: {holt_metrics_24h['RMSE']:.2f}, MAE: {holt_metrics_24h['MAE']:.2f}, MAPE: {holt_metrics_24h['MAPE']:.2f}%")
        
        # Create comparison plots
        self.plot_comparison(actual_24h, sarimax_24h, holt_24h, sarimax_metrics_24h, holt_metrics_24h)
        
        # Print final comparison
        self.print_final_comparison(sarimax_metrics_24h, holt_metrics_24h)
    
    def plot_comparison(self, actual, sarimax_pred, holt_pred, sarimax_metrics, holt_metrics):
        """Create comparison plots"""
        fig, axes = plt.subplots(1, 2, figsize=(15, 6))
        fig.suptitle('SARIMAX vs Holt-Winter: Forecast Comparison', fontsize=14, fontweight='bold')
        
        # Time series comparison
        axes[0].plot(actual.index, actual.values, 'o-', label='Actual', linewidth=2, markersize=4)
        axes[0].plot(sarimax_pred.index, sarimax_pred.values, 's-', label='SARIMAX', linewidth=2, markersize=4)
        axes[0].plot(holt_pred.index, holt_pred.values, '^-', label='Holt-Winter', linewidth=2, markersize=4)
        axes[0].set_title('24-Hour Forecast Comparison')
        axes[0].set_ylabel('Demand')
        axes[0].legend()
        axes[0].grid(True, alpha=0.3)
        axes[0].tick_params(axis='x', rotation=45)
        
        # Metrics comparison
        metrics = ['RMSE', 'MAE', 'MAPE']
        sarimax_vals = [sarimax_metrics[m] for m in metrics]
        holt_vals = [holt_metrics[m] for m in metrics]
        
        x = np.arange(len(metrics))
        width = 0.35
        
        axes[1].bar(x - width/2, sarimax_vals, width, label='SARIMAX', alpha=0.8)
        axes[1].bar(x + width/2, holt_vals, width, label='Holt-Winter', alpha=0.8)
        axes[1].set_title('Forecast Metrics Comparison')
        axes[1].set_ylabel('Metric Value')
        axes[1].set_xticks(x)
        axes[1].set_xticklabels(metrics)
        axes[1].legend()
        axes[1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()
    
    def print_final_comparison(self, sarimax_metrics, holt_metrics):
        """Print final comparison summary"""
        print("\n" + "=" * 60)
        print("FINAL COMPARISON SUMMARY")
        print("=" * 60)
        
        print(f"{'Metric':<10} {'SARIMAX':<15} {'Holt-Winter':<15} {'Winner':<15}")
        print("-" * 55)
        
        sarimax_wins = 0
        holt_wins = 0
        
        for metric in ['RMSE', 'MAE', 'MAPE']:
            sarimax_val = sarimax_metrics[metric]
            holt_val = holt_metrics[metric]
            winner = 'SARIMAX' if sarimax_val < holt_val else 'Holt-Winter'
            if winner == 'SARIMAX':
                sarimax_wins += 1
            else:
                holt_wins += 1
            print(f"{metric:<10} {sarimax_val:<15.2f} {holt_val:<15.2f} {winner:<15}")
        
        print("\n" + "=" * 60)
        if sarimax_wins > holt_wins:
            print("🏆 SARIMAX is the OVERALL WINNER!")
        elif holt_wins > sarimax_wins:
            print("🏆 HOLT-WINTER is the OVERALL WINNER!")
        else:
            print("🤝 IT'S A TIE!")
        
        print("\nKEY INSIGHTS:")
        print("• SARIMAX can leverage exogenous variables (Temperature, etc.)")
        print("• Holt-Winter focuses on time series patterns only")
        print("• Both models capture seasonal patterns effectively")
        print("• Performance depends on data characteristics and forecast horizon")
    
    def run_complete_analysis(self):
        """Run the complete analysis"""
        print("🚀 STARTING FAST DEMO: TIME SERIES FORECASTING ANALYSIS")
        print("📊 Electric Demand Forecasting: SARIMAX vs Holt-Winter")
        print("⚡ Optimized for Quick Execution\n")
        
        # Phase 1: Data Preparation and EDA
        self.load_and_prepare_data()
        self.handle_exogenous_variables()
        self.exploratory_data_analysis()
        
        # Phase 2: Data Splitting and Model Training
        self.split_data_chronologically()
        self.train_sarimax_model()
        self.train_holt_winter_model()
        
        # Phase 3 & 4: Evaluation and Comparison
        self.evaluate_and_compare_models()
        
        print("\n🎉 FAST DEMO COMPLETED SUCCESSFULLY!")
        print("📈 Check the generated plots for visual comparisons")


def main():
    """Main execution function"""
    # Initialize the forecaster with smaller sample for demo
    forecaster = FastTimeSeriesForecaster('electric_demand_1h.csv', sample_size=2000)
    
    # Run complete analysis
    forecaster.run_complete_analysis()


if __name__ == "__main__":
    main()
